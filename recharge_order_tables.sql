-- 卡续充充值订单表
CREATE TABLE `recharge_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `recharge_order_no` VARCHAR(64) NOT NULL COMMENT '充值订单号',
  `contract_no` VARCHAR(64) NOT NULL COMMENT '合同编码',
  `customer_code` VARCHAR(64) NOT NULL COMMENT '客户编码',
  `customer_name` VARCHAR(255) NOT NULL COMMENT '客户名称',
  `seller_id` VARCHAR(64) NOT NULL COMMENT '销售员万信号',
  `seller` VARCHAR(64) NOT NULL COMMENT '销售员姓名',
  `area_code` VARCHAR(64) NOT NULL COMMENT '区域编码',
  `area_name` VARCHAR(128) NOT NULL COMMENT '区域名称',
  `cinema_inner_code` VARCHAR(64) NOT NULL COMMENT '影城内码',
  `cinema_name` VARCHAR(128) NOT NULL COMMENT '影城名称',
  `every_card_recharge_amount` INT DEFAULT NULL COMMENT '每张卡充值金额（分）',
  `every_card_present_amount` INT DEFAULT NULL COMMENT '每张卡赠送金额（分）',
  `count` INT NOT NULL COMMENT '卡数量',
  `amount` INT NOT NULL COMMENT '总充值金额（含赠送金额）分',
  `status` TINYINT NOT NULL COMMENT '状态 1:待充值, 2:部分充值, 3:充值成功, 4:充值失败',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_recharge_order_no` (`recharge_order_no`),
  KEY `idx_contract_no` (`contract_no`),
  KEY `idx_customer_code` (`customer_code`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_seller` (`seller`),
  KEY `idx_area_code` (`area_code`),
  KEY `idx_cinema_inner_code` (`cinema_inner_code`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡续充充值订单表';

-- 卡续充充值订单卡信息表
CREATE TABLE `recharge_order_card_info` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recharge_order_no` VARCHAR(64) NOT NULL COMMENT '充值订单号',
  `card_no` VARCHAR(64) NOT NULL COMMENT '卡号（脱敏展示）',
  `card_type_code` VARCHAR(64) NOT NULL COMMENT '卡类型编码',
  `card_type_name` VARCHAR(128) NOT NULL COMMENT '卡类型名称',
  `recharge_amount` INT NOT NULL COMMENT '充值金额（分）',
  `present_amount` INT NOT NULL COMMENT '赠送金额（分）',
  `change_balance` INT NOT NULL COMMENT '余额变动（充值金额+赠送金额）（分）',
  `before_balance` INT NOT NULL COMMENT '充值前余额（分）',
  `after_balance` INT NOT NULL COMMENT '充值后余额（分）',
  `status` TINYINT NOT NULL COMMENT '充值状态 1:待充值 2:充值中 3:充值成功 4:充值失败',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `recharge_time` DATETIME DEFAULT NULL COMMENT '充值时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_recharge_order_no` (`recharge_order_no`),
  KEY `idx_card_no` (`card_no`),
  KEY `idx_status` (`status`),
  KEY `idx_recharge_time` (`recharge_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡续充充值订单卡信息表';

-- 卡续充充值订单日志表
CREATE TABLE `recharge_order_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `batch_no` VARCHAR(64) DEFAULT NULL COMMENT '充值流水号（批次号）',
  `recharge_order_no` VARCHAR(64) NOT NULL COMMENT '充值订单号',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  `operation_type` TINYINT NOT NULL COMMENT '操作类型 1.创建充值订单 2.作废充值订单 3.充值',
  `operation_log` VARCHAR(512) NOT NULL COMMENT '操作日志',
  `operator` VARCHAR(64) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  KEY `idx_recharge_order_no` (`recharge_order_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡续充充值订单日志表';

-- 每批次充值卡卡号明细表
CREATE TABLE `batch_recharge_card_detail_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `log_id` BIGINT NOT NULL COMMENT '日志id',
  `batch_no` VARCHAR(64) DEFAULT NULL COMMENT '充值流水号（批次号）',
  `card_no` VARCHAR(64) NOT NULL COMMENT '卡号',
  PRIMARY KEY (`id`),
  KEY `idx_log_id` (`log_id`),
  KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每批次充值卡卡号明细表';