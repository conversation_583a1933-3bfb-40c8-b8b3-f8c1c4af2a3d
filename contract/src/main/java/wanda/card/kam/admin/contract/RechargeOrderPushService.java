package wanda.card.kam.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.stark.core.data.R;

import javax.validation.Valid;

/**
 * 充值订单推送服务
 *
 * <AUTHOR>
 */
@Validated
@FeignClient(contextId = "rechargeOrderPushService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface RechargeOrderPushService {

    /**
     * 推送充值订单
     *
     * @param request 推送请求
     * @return 推送响应
     */
    @PostMapping("/recharge-order/push")
    R<Void> pushRechargeOrder(@Valid @RequestBody RechargeOrderPushRequest request);
}
