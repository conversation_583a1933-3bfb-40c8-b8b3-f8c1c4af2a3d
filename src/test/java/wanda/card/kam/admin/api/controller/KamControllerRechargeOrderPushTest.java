package wanda.card.kam.admin.api.controller;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import wanda.card.kam.admin.api.ApiApplication;
import wanda.card.kam.admin.api.model.RechargeOrderPushModel;
import wanda.card.kam.admin.api.model.PushMapper;
import wanda.card.kam.admin.contract.RechargeOrderPushService;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto;
import wanda.stark.core.data.R;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(KamController.class)
class KamControllerRechargeOrderPushTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PushMapper pushMapper;

    @MockBean
    private RechargeOrderPushService rechargeOrderPushService;

    @Test
    void testRechargeOrderPushSuccess() throws Exception {
        // 准备测试数据
        RechargeOrderPushModel.RechargeOrderPushRequest apiRequest = buildApiRequest();
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = buildRpcRequest();
        
        // 模拟mapper转换
        when(pushMapper.convertToRpcRequest(any(RechargeOrderPushModel.RechargeOrderPushRequest.class)))
                .thenReturn(rpcRequest);
        
        // 模拟服务调用成功
        R rpcResponse = R.success();
        when(rechargeOrderPushService.pushRechargeOrder(any(RechargeOrderPushDto.RechargeOrderPushRequest.class)))
                .thenReturn(rpcResponse);
        
        // 执行测试
        mockMvc.perform(post("/card-kam/ysht/recharge-order/push")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(apiRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(0))
                .andExpect(jsonPath("$.msg").value("请求成功"));
    }

    @Test
    void testRechargeOrderPushFailure() throws Exception {
        // 准备测试数据
        RechargeOrderPushModel.RechargeOrderPushRequest apiRequest = buildApiRequest();
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = buildRpcRequest();
        
        // 模拟mapper转换
        when(pushMapper.convertToRpcRequest(any(RechargeOrderPushModel.RechargeOrderPushRequest.class)))
                .thenReturn(rpcRequest);
        
        // 模拟服务调用失败
        R rpcResponse = R.fail("推送失败");
        when(rechargeOrderPushService.pushRechargeOrder(any(RechargeOrderPushDto.RechargeOrderPushRequest.class)))
                .thenReturn(rpcResponse);
        
        // 执行测试
        mockMvc.perform(post("/card-kam/ysht/recharge-order/push")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(apiRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(-1))
                .andExpect(jsonPath("$.msg").value("推送失败"));
    }

    @Test
    void testRechargeOrderPushException() throws Exception {
        // 准备测试数据
        RechargeOrderPushModel.RechargeOrderPushRequest apiRequest = buildApiRequest();
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = buildRpcRequest();
        
        // 模拟mapper转换
        when(pushMapper.convertToRpcRequest(any(RechargeOrderPushModel.RechargeOrderPushRequest.class)))
                .thenReturn(rpcRequest);
        
        // 模拟服务调用异常
        when(rechargeOrderPushService.pushRechargeOrder(any(RechargeOrderPushDto.RechargeOrderPushRequest.class)))
                .thenThrow(new RuntimeException("网络异常"));
        
        // 执行测试
        mockMvc.perform(post("/card-kam/ysht/recharge-order/push")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(apiRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(-1))
                .andExpect(jsonPath("$.msg").value("网络异常"));
    }

    @Test
    void testRechargeOrderPushValidationFailure() throws Exception {
        // 准备无效的测试数据 - 缺少必要字段
        RechargeOrderPushModel.RechargeOrderPushRequest apiRequest = new RechargeOrderPushModel.RechargeOrderPushRequest();
        // 不设置任何字段，让校验失败
        
        // 执行测试
        mockMvc.perform(post("/card-kam/ysht/recharge-order/push")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(apiRequest)))
                .andExpect(status().isBadRequest());
    }

    private RechargeOrderPushModel.RechargeOrderPushRequest buildApiRequest() {
        RechargeOrderPushModel.RechargeOrderPushRequest request = new RechargeOrderPushModel.RechargeOrderPushRequest();
        request.setRechargeOrderNo("RO202201010001");
        request.setContractNo("CNT202201010001");
        request.setCustomerCode("CUST001");
        request.setCustomerName("测试客户");
        request.setSellerId("SELLER001");
        request.setSeller("测试销售");
        request.setAreaCode("AREA001");
        request.setAreaName("测试区域");
        request.setCinemaInnerCode("CINEMA001");
        request.setCinemaName("测试影院");
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(1000);
        request.setCount(2);
        request.setAmount(22000);
        request.setOperator("测试操作员");
        
        List<RechargeOrderPushModel.RechargeOrderCardPushInfo> cardInfos = new ArrayList<>();
        RechargeOrderPushModel.RechargeOrderCardPushInfo cardInfo1 = new RechargeOrderPushModel.RechargeOrderCardPushInfo();
        cardInfo1.setCardNo("CARD000001");
        cardInfo1.setCardTypeCode("CARD_TYPE001");
        cardInfo1.setCardTypeName("测试卡类型1");
        
        RechargeOrderPushModel.RechargeOrderCardPushInfo cardInfo2 = new RechargeOrderPushModel.RechargeOrderCardPushInfo();
        cardInfo2.setCardNo("CARD000002");
        cardInfo2.setCardTypeCode("CARD_TYPE001");
        cardInfo2.setCardTypeName("测试卡类型1");
        
        cardInfos.add(cardInfo1);
        cardInfos.add(cardInfo2);
        request.setCardInfos(cardInfos);
        
        return request;
    }
    
    private RechargeOrderPushDto.RechargeOrderPushRequest buildRpcRequest() {
        RechargeOrderPushDto.RechargeOrderPushRequest request = new RechargeOrderPushDto.RechargeOrderPushRequest();
        request.setRechargeOrderNo("RO202201010001");
        request.setContractNo("CNT202201010001");
        request.setCustomerCode("CUST001");
        request.setCustomerName("测试客户");
        request.setSellerId("SELLER001");
        request.setSeller("测试销售");
        request.setAreaCode("AREA001");
        request.setAreaName("测试区域");
        request.setCinemaInnerCode("CINEMA001");
        request.setCinemaName("测试影院");
        request.setEveryCardRechargeAmount(10000);
        request.setEveryCardPresentAmount(1000);
        request.setCount(2);
        request.setAmount(22000);
        request.setOperator("测试操作员");
        
        List<RechargeOrderPushDto.RechargeOrderCardPushInfo> cardInfos = new ArrayList<>();
        RechargeOrderPushDto.RechargeOrderCardPushInfo cardInfo1 = new RechargeOrderPushDto.RechargeOrderCardPushInfo();
        cardInfo1.setCardNo("CARD000001");
        cardInfo1.setCardTypeCode("CARD_TYPE001");
        cardInfo1.setCardTypeName("测试卡类型1");
        
        RechargeOrderPushDto.RechargeOrderCardPushInfo cardInfo2 = new RechargeOrderPushDto.RechargeOrderCardPushInfo();
        cardInfo2.setCardNo("CARD000002");
        cardInfo2.setCardTypeCode("CARD_TYPE001");
        cardInfo2.setCardTypeName("测试卡类型1");
        
        cardInfos.add(cardInfo1);
        cardInfos.add(cardInfo2);
        request.setCardInfos(cardInfos);
        
        return request;
    }
}