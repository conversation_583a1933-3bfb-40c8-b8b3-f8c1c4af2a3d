package wanda.card.kam.admin.service.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.card.kam.admin.contract.RechargeOrderPushService;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderCardPushInfo;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.service.ServiceApplication;
import wanda.card.kam.admin.service.biz.RechargeOrderPushServiceBiz;
import wanda.stark.core.test.TestBase;

import java.util.Arrays;

/**
 * 充值订单推送服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = {ServiceApplication.class})
public class RechargeOrderPushServiceTest extends TestBase {

    @Autowired
    private RechargeOrderPushService rechargeOrderPushService;

    @Test
    public void testPushRechargeOrder() {
        // 构建测试请求
        RechargeOrderPushRequest request = new RechargeOrderPushRequest();
        request.setRechargeOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        request.setContractNo("CT202501010001");
        request.setCustomerCode("CU001");
        request.setCustomerName("测试客户");
        request.setSellerId("S001");
        request.setSeller("张三");
        request.setAreaCode("A001");
        request.setAreaName("华北区");
        request.setCinemaInnerCode("C001");
        request.setCinemaName("万达影城测试店");

        // 构建1000张卡的测试数据
        RechargeOrderCardPushInfo[] cardInfos = new RechargeOrderCardPushInfo[1000];
        for (int i = 0; i < 1000; i++) {
            RechargeOrderCardPushInfo cardInfo = new RechargeOrderCardPushInfo();
            cardInfo.setCardNo("CARD" + String.format("%012d", i)); // 生成16位卡号
            cardInfo.setCardTypeCode("120000121");
            cardInfo.setCardTypeName("储值卡");
            cardInfos[i] = cardInfo;
        }

//        request.setCardInfos(Arrays.asList(cardInfos));

        // 调用服务
        rechargeOrderPushService.pushRechargeOrder(request);
    }
}
